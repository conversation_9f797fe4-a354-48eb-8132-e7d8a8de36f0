'use client'

import { useState, useEffect } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { motion } from 'framer-motion'
import { Copy, Download, Edit, Eye, CheckCircle, Save, X } from 'lucide-react'

interface BlogStyleDisplayProps {
  content: string
  title?: string
  onContentChange?: (content: string) => void
  editable?: boolean
  className?: string
}

export default function BlogStyleDisplay({ 
  content, 
  title, 
  onContentChange, 
  editable = true,
  className = ""
}: BlogStyleDisplayProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState(content)
  const [copied, setCopied] = useState(false)

  useEffect(() => {
    setEditedContent(content)
  }, [content])

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(isEditing ? editedContent : content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const handleDownload = () => {
    const contentToDownload = isEditing ? editedContent : content
    const blob = new Blob([contentToDownload], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title || 'blog-post'}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleSaveEdit = () => {
    if (onContentChange) {
      onContentChange(editedContent)
    }
    setIsEditing(false)
  }

  const handleCancelEdit = () => {
    setEditedContent(content)
    setIsEditing(false)
  }

  return (
    <div className={`sky-bg min-h-screen ${className}`}>
      {/* Toolbar */}
      <div className="bg-white/30 backdrop-blur-xl border-b border-sky-200/50 sticky top-0 z-10 p-4 shadow-lg">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <div>
            {title && <h1 className="text-xl font-bold sky-heading">{title}</h1>}
            <p className="text-sm sky-accent">
              {isEditing ? 'Editing Mode' : 'Preview Mode'}
            </p>
          </div>

          <div className="flex items-center space-x-2">
            {editable && (
              <>
                {isEditing ? (
                  <>
                    <motion.button
                      onClick={handleSaveEdit}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 px-4 py-2 bg-emerald-500/80 backdrop-blur-sm hover:bg-emerald-600/90 text-white rounded-xl transition-all text-sm shadow-lg border border-white/20"
                    >
                      <Save className="w-4 h-4" />
                      <span>Save</span>
                    </motion.button>
                    <motion.button
                      onClick={handleCancelEdit}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 px-4 py-2 bg-slate-500/80 backdrop-blur-sm hover:bg-slate-600/90 text-white rounded-xl transition-all text-sm shadow-lg border border-white/20"
                    >
                      <X className="w-4 h-4" />
                      <span>Cancel</span>
                    </motion.button>
                  </>
                ) : (
                  <motion.button
                    onClick={() => setIsEditing(true)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-3 bg-white/30 backdrop-blur-sm hover:bg-white/40 border border-sky-200/50 rounded-xl transition-all shadow-lg"
                    title="Edit Content"
                  >
                    <Edit className="w-4 h-4 text-sky-700" />
                  </motion.button>
                )}
              </>
            )}

            <motion.button
              onClick={handleCopy}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-3 bg-white/30 backdrop-blur-sm hover:bg-white/40 border border-sky-200/50 rounded-xl transition-all shadow-lg"
              title="Copy to Clipboard"
            >
              {copied ? (
                <CheckCircle className="w-4 h-4 text-emerald-600" />
              ) : (
                <Copy className="w-4 h-4 text-sky-700" />
              )}
            </motion.button>

            <motion.button
              onClick={handleDownload}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-3 bg-white/30 backdrop-blur-sm hover:bg-white/40 border border-sky-200/50 rounded-xl transition-all shadow-lg"
              title="Download as Markdown"
            >
              <Download className="w-4 h-4 text-sky-700" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-4xl mx-auto"
        >
          {isEditing ? (
            <div className="sky-card p-6 shadow-xl">
              <div className="mb-4">
                <h2 className="text-lg font-semibold sky-heading mb-2">Edit Content</h2>
                <p className="text-sm sky-text opacity-75">
                  Edit your content in Markdown format. Changes will be reflected in the preview.
                </p>
              </div>
              <textarea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="w-full h-96 p-4 border border-sky-200/50 rounded-xl sky-text bg-white/30 backdrop-blur-sm focus:border-sky-400 focus:ring-2 focus:ring-sky-400/20 transition-all resize-none font-mono text-sm leading-relaxed shadow-inner"
                placeholder="Edit your blog post content in Markdown..."
              />
              <div className="mt-4 text-xs sky-text opacity-60">
                <p>Tip: Use Markdown syntax for formatting (# for headings, ** for bold, * for italic, etc.)</p>
              </div>
            </div>
          ) : (
            <div className="sky-card p-8 shadow-xl">
              <div className="prose prose-lg max-w-none">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    h1: ({ children }) => (
                      <h1 className="text-4xl font-bold sky-heading mb-8 leading-tight border-b-2 border-sky-300/50 pb-4 bg-gradient-to-r from-sky-600 to-cyan-600 bg-clip-text text-transparent">
                        {children}
                      </h1>
                    ),
                    h2: ({ children }) => (
                      <h2 className="text-3xl font-bold sky-heading mb-6 mt-12 leading-tight pl-4 border-l-4 border-sky-400 bg-white/20 backdrop-blur-sm rounded-r-lg py-2">
                        {children}
                      </h2>
                    ),
                    h3: ({ children }) => (
                      <h3 className="text-2xl font-semibold sky-heading mb-4 mt-8">
                        {children}
                      </h3>
                    ),
                    h4: ({ children }) => (
                      <h4 className="text-xl font-semibold sky-heading mb-3 mt-6">
                        {children}
                      </h4>
                    ),
                    p: ({ children }) => (
                      <p className="sky-text leading-relaxed mb-6 text-lg">
                        {children}
                      </p>
                    ),
                    ul: ({ children }) => (
                      <ul className="space-y-3 mb-8 ml-6 list-disc list-outside">
                        {children}
                      </ul>
                    ),
                    ol: ({ children }) => (
                      <ol className="space-y-3 mb-8 ml-6 list-decimal list-outside">
                        {children}
                      </ol>
                    ),
                    li: ({ children }) => (
                      <li className="sky-text text-lg leading-relaxed hover:text-sky-800 transition-colors">
                        {children}
                      </li>
                    ),
                    strong: ({ children }) => (
                      <strong className="font-bold text-sky-800 bg-gradient-to-r from-sky-100 to-cyan-100 px-2 py-1 rounded-lg shadow-sm backdrop-blur-sm">
                        {children}
                      </strong>
                    ),
                    em: ({ children }) => (
                      <em className="italic sky-accent font-medium">{children}</em>
                    ),
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-sky-400 pl-8 pr-4 italic sky-text my-8 bg-white/30 backdrop-blur-sm py-6 rounded-r-xl shadow-lg relative border border-sky-200/30">
                        <div className="text-xl leading-relaxed">
                          <span className="absolute -top-2 -left-2 text-3xl text-sky-400 opacity-50">"</span>
                          {children}
                          <span className="absolute -bottom-4 -right-2 text-3xl text-sky-400 opacity-50">"</span>
                        </div>
                      </blockquote>
                    ),
                    code: ({ children }) => (
                      <code className="bg-sky-100/50 text-sky-800 px-3 py-1 rounded-lg text-base font-mono border border-sky-200/50 shadow-sm backdrop-blur-sm">
                        {children}
                      </code>
                    ),
                    pre: ({ children }) => (
                      <pre className="bg-gradient-to-br from-slate-800 to-sky-900 text-sky-100 p-6 rounded-xl overflow-x-auto my-6 border border-sky-700/50 shadow-xl backdrop-blur-sm">
                        <code className="text-sm leading-relaxed">{children}</code>
                      </pre>
                    ),
                    table: ({ children }) => (
                      <div className="overflow-x-auto my-8 rounded-xl shadow-lg">
                        <table className="min-w-full border-collapse bg-white/30 backdrop-blur-sm border border-sky-200/30 rounded-xl overflow-hidden">
                          {children}
                        </table>
                      </div>
                    ),
                    th: ({ children }) => (
                      <th className="border border-sky-200/30 px-6 py-4 bg-gradient-to-r from-sky-200/50 to-cyan-200/50 sky-heading font-semibold text-left backdrop-blur-sm">
                        {children}
                      </th>
                    ),
                    td: ({ children }) => (
                      <td className="border border-sky-200/30 px-6 py-4 sky-text bg-white/20 hover:bg-white/30 transition-colors backdrop-blur-sm">
                        {children}
                      </td>
                    ),
                    a: ({ children, href }) => (
                      <a
                        href={href}
                        className="sky-accent hover:text-sky-700 underline decoration-sky-400 hover:decoration-sky-600 transition-all duration-200 font-medium"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {children}
                      </a>
                    ),
                    hr: () => (
                      <hr className="my-8 border-0 h-px bg-gradient-to-r from-transparent via-sky-300/50 to-transparent" />
                    )
                  }}
                >
                  {content}
                </ReactMarkdown>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
