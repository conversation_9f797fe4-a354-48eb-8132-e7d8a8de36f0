'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import BlogStyleDisplay from '@/components/BlogStyleDisplay'

export default function BlogDisplayPage() {
  const searchParams = useSearchParams()
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('')

  useEffect(() => {
    const contentParam = searchParams.get('content')
    const titleParam = searchParams.get('title')

    if (contentParam) {
      try {
        const decodedContent = decodeURIComponent(contentParam)
        setContent(decodedContent)
      } catch (error) {
        console.error('Error decoding content:', error)
        setContent('Error: Content could not be decoded. Please try generating a new blog post.')
      }
    }

    if (titleParam) {
      try {
        setTitle(decodeURIComponent(titleParam))
      } catch (error) {
        console.error('Error decoding title:', error)
        setTitle('Blog Post')
      }
    }
  }, [searchParams])

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
  }

  if (!content) {
    return (
      <div className="min-h-screen sky-bg flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📝</div>
          <h1 className="text-2xl font-bold sky-heading mb-2">No Content Found</h1>
          <p className="sky-text mb-6">No blog content was provided. Please generate a new blog post.</p>
          <Link href="/blog-generator">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-6 py-3 bg-sky-500/80 backdrop-blur-sm hover:bg-sky-600/90 text-white rounded-xl transition-all shadow-lg border border-white/20"
            >
              Generate Blog Post
            </motion.button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="relative">
      {/* Back Button */}
      <div className="absolute top-4 left-4 z-20">
        <Link href="/blog-generator">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-3 bg-white/30 backdrop-blur-sm hover:bg-white/40 border border-sky-200/50 rounded-xl transition-all shadow-lg"
          >
            <ArrowLeft className="w-5 h-5 text-sky-700" />
          </motion.button>
        </Link>
      </div>

      {/* Blog Display Component */}
      <BlogStyleDisplay
        content={content}
        title={title}
        onContentChange={handleContentChange}
        editable={true}
      />
    </div>
  )
}
